<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <!-- Theme color is defined in the manifest file -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <title>The Badhees - Premium Handcrafted Furniture & Home Decor</title>
    <meta name="description" content="The Badhees - Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta name="keywords" content="furniture, home decor, handcrafted, premium, interior design, custom furniture, The Badhees" />
    <meta name="author" content="The Badhees" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.thebadhees.com/" />
    <meta property="og:title" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <meta property="og:description" content="Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta property="og:image" content="/logo.svg" />
    <meta property="og:site_name" content="The Badhees" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://www.thebadhees.com/" />
    <meta property="twitter:title" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <meta property="twitter:description" content="Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta property="twitter:image" content="/logo.svg" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Favicons and Icons -->
    <link rel="manifest" href="/manifest.webmanifest" />
    <link rel="apple-touch-icon" href="/icons/apple-touch-icon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/icons/favicon-32x32.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/icons/favicon-16x16.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <meta itemprop="description" content="Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta itemprop="image" content="/logo.svg" />
    <script>
      // Check if we have a redirect path from the 404 page
      (function() {
        const redirectPath = sessionStorage.getItem('redirectPath');
        if (redirectPath) {
          sessionStorage.removeItem('redirectPath');
          // We'll let the app handle the routing once it loads
          window.history.replaceState(null, null, redirectPath);
        }
      })();
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- External script removed temporarily for debugging -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
