import { useEffect } from 'react';

interface PerformanceMetrics {
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
}

/**
 * Performance monitoring component that tracks Core Web Vitals
 */
export const PerformanceMonitor = () => {
  useEffect(() => {
    // Only run in production
    if (import.meta.env.DEV) return;

    const metrics: PerformanceMetrics = {};

    // Measure Time to First Byte (TTFB)
    const measureTTFB = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        metrics.ttfb = navigation.responseStart - navigation.requestStart;
      }
    };

    // Measure First Contentful Paint (FCP)
    const measureFCP = () => {
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
      }
    };

    // Measure Largest Contentful Paint (LCP)
    const measureLCP = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          metrics.lcp = lastEntry.startTime;
        });
        
        try {
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          // LCP not supported
        }
      }
    };

    // Measure First Input Delay (FID)
    const measureFID = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            metrics.fid = entry.processingStart - entry.startTime;
          });
        });
        
        try {
          observer.observe({ entryTypes: ['first-input'] });
        } catch (e) {
          // FID not supported
        }
      }
    };

    // Measure Cumulative Layout Shift (CLS)
    const measureCLS = () => {
      if ('PerformanceObserver' in window) {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              metrics.cls = clsValue;
            }
          });
        });
        
        try {
          observer.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          // CLS not supported
        }
      }
    };

    // Send metrics to analytics (replace with your analytics service)
    const sendMetrics = () => {
      // Only send if we have meaningful data
      if (Object.keys(metrics).length > 0) {
        // Example: Send to Google Analytics, PostHog, or your analytics service
        console.log('Performance Metrics:', metrics);
        
        // You can replace this with your actual analytics implementation
        // Example for Google Analytics:
        // gtag('event', 'web_vitals', {
        //   event_category: 'Performance',
        //   event_label: 'Core Web Vitals',
        //   value: Math.round(metrics.lcp || 0),
        //   custom_map: {
        //     metric_lcp: metrics.lcp,
        //     metric_fid: metrics.fid,
        //     metric_cls: metrics.cls,
        //     metric_fcp: metrics.fcp,
        //     metric_ttfb: metrics.ttfb
        //   }
        // });
      }
    };

    // Initialize measurements
    measureTTFB();
    measureFCP();
    measureLCP();
    measureFID();
    measureCLS();

    // Send metrics after page load
    const sendMetricsTimeout = setTimeout(sendMetrics, 5000); // Send after 5 seconds

    // Send metrics before page unload
    const handleBeforeUnload = () => {
      sendMetrics();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      clearTimeout(sendMetricsTimeout);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return null; // This component doesn't render anything
};

/**
 * Hook to measure custom performance metrics
 */
export const usePerformanceMetric = (metricName: string) => {
  const startMeasure = () => {
    if (typeof window !== 'undefined' && window.performance) {
      performance.mark(`${metricName}-start`);
    }
  };

  const endMeasure = () => {
    if (typeof window !== 'undefined' && window.performance) {
      performance.mark(`${metricName}-end`);
      performance.measure(metricName, `${metricName}-start`, `${metricName}-end`);
      
      const measure = performance.getEntriesByName(metricName)[0];
      if (measure && !import.meta.env.DEV) {
        console.log(`${metricName}: ${measure.duration}ms`);
      }
      
      return measure?.duration;
    }
    return null;
  };

  return { startMeasure, endMeasure };
};

/**
 * Component to measure page load performance
 */
export const PageLoadMonitor = ({ pageName }: { pageName: string }) => {
  useEffect(() => {
    const { startMeasure, endMeasure } = usePerformanceMetric(`page-load-${pageName}`);
    
    startMeasure();
    
    // Measure when page is fully loaded
    const handleLoad = () => {
      endMeasure();
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
      return () => window.removeEventListener('load', handleLoad);
    }
  }, [pageName]);

  return null;
};

export default PerformanceMonitor;
