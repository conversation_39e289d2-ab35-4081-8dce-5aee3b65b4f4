
import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>R<PERSON>, ChevronLeft, ChevronRight } from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

interface BannerSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  secondaryCtaText: string;
  secondaryCtaLink: string;
  image: string;
  position: "left" | "right" | "center";
  theme: "light" | "dark";
}

const HeroBanner = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const bannerSlides: BannerSlide[] = [
    {
      id: 1,
      title: "Timeless Design For Modern Living",
      subtitle: "Handcrafted Luxury",
      description: "Discover furniture that transcends trends, combining impeccable craftsmanship with minimalist elegance for spaces that inspire.",
      ctaText: "Explore Products",
      ctaLink: "/products",
      secondaryCtaText: "Our Story",
      secondaryCtaLink: "/about",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//homepageherobanner1",
      position: "left",
      theme: "light"
    },
    {
      id: 2,
      title: "Elevate Your Living Space",
      subtitle: "New Arrivals",
      description: "Transform your home with our latest collection of contemporary furniture designed for comfort and style.",
      ctaText: "Explore Interior Solutions",
      ctaLink: "/custom-interiors",
      secondaryCtaText: "View Bestsellers",
      secondaryCtaLink: "/products",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//homepageherobanner2.jpeg",
      position: "left",
      theme: "light"
    },
    {
      id: 3,
      title: "Sustainable Craftsmanship",
      subtitle: "Eco-Friendly Collection",
      description: ".",
      ctaText: "Shop Sustainable",
      ctaLink: "/products",
      secondaryCtaText: "Learn More",
      secondaryCtaLink: "/about",
      image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//homepageherobanner3.jpeg",
      position: "left",
      theme: "light"
    }
  ];

  const goToNextSlide = useCallback(() => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setCurrentSlide((prevSlide) =>
      prevSlide === bannerSlides.length - 1 ? 0 : prevSlide + 1
    );

    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  }, [bannerSlides.length, isTransitioning]);

  const goToPrevSlide = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setCurrentSlide((prevSlide) =>
      prevSlide === 0 ? bannerSlides.length - 1 : prevSlide - 1
    );

    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentSlide) return;

    setIsTransitioning(true);
    setCurrentSlide(index);

    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  // Auto-rotate slides every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      goToNextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [goToNextSlide]);

  const currentBanner = bannerSlides[currentSlide];

  return (
    <section className="relative w-full h-[60vh] md:h-[70vh] min-h-[400px] md:min-h-[500px] overflow-hidden">
      {/* Slide container */}
      <div className="relative w-full h-full">
        {bannerSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={cn(
              "absolute inset-0 w-full h-full transition-opacity duration-1000",
              currentSlide === index ? "opacity-100 z-10" : "opacity-0 z-0"
            )}
          >
            {/* Background image */}
            <div className="absolute inset-0 w-full h-full">
              <img
                src={slide.image}
                alt={slide.title}
                className="w-full h-full object-cover"
                loading={index === 0 ? "eager" : "lazy"}
              />
              <div className={cn(
                "absolute inset-0",
                slide.theme === "dark"
                  ? "bg-gradient-to-r from-black/70 via-black/50 to-black/30"
                  : "bg-gradient-to-r from-white/60 via-white/40 to-transparent"
              )}></div>
            </div>

            {/* Content */}
            <div className="absolute inset-0 flex items-center max-w-[1400px] mx-auto px-4 sm:px-8">
              <div className={cn(
                "max-w-xl space-y-6",
                slide.position === "left" ? "ml-0 mr-auto" :
                slide.position === "right" ? "ml-auto mr-0" :
                "mx-auto text-center"
              )}>
                <div className={cn(
                  "inline-block px-2 py-1 mb-1 text-xs font-medium tracking-wider rounded-full uppercase animate-fadeIn",
                  slide.theme === "dark"
                    ? "text-white bg-white/20"
                    : "text-badhees-accent bg-badhees-accent/10"
                )}>
                  {slide.subtitle}
                </div>

                <h1 className={cn(
                  "text-3xl md:text-4xl lg:text-5xl font-bold leading-tight animate-slideUp",
                  slide.theme === "dark" ? "text-white" : "text-badhees-800"
                )}>
                  {slide.title}
                </h1>

                <p className={cn(
                  "text-sm md:text-lg lg:text-xl animate-slideUp animate-delay-100 line-clamp-3 md:line-clamp-none",
                  slide.theme === "dark" ? "text-white/90" : "text-badhees-600"
                )}>
                  {slide.description}
                </p>

                <div className="flex flex-wrap gap-2 md:gap-4 pt-2 animate-slideUp animate-delay-200">
                  <Link
                    to={slide.ctaLink}
                    className={cn(
                      "btn-primary text-sm md:text-base py-2 px-3 md:px-4",
                      slide.theme === "dark" && "bg-white text-badhees-800 hover:bg-badhees-50"
                    )}
                  >
                    {slide.ctaText}
                    <ArrowRight className="ml-1 md:ml-2 h-3 w-3 md:h-4 md:w-4" />
                  </Link>
                  <Link
                    to={slide.secondaryCtaLink}
                    className={cn(
                      "btn-secondary text-sm md:text-base py-2 px-3 md:px-4",
                      slide.theme === "dark" && "border-white text-white hover:bg-white/10"
                    )}
                  >
                    {slide.secondaryCtaText}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default HeroBanner;
