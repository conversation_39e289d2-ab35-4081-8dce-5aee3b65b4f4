/**
 * Supabase Helper Utilities
 *
 * This module provides common utilities for working with Supabase
 * to reduce code duplication across service files.
 */

import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

/**
 * Standard error handler for Supabase operations
 * @param error The error object
 * @param context Additional context for the error
 * @param showToast Whether to show a toast notification
 * @returns Formatted error message
 */
export const handleSupabaseError = (
  error: unknown,
  context: string,
  showToast: boolean = true
): string => {
  const err = error as Error;
  console.error(`Error in ${context}:`, err);

  // Log more detailed error information for network issues
  if (err.message?.includes('fetch')) {
    console.error('Network error details:', {
      type: 'Network Error',
      message: 'This might be a CORS issue or the Supabase project is not accessible',
      originalError: err.toString()
    });
  }

  // Only show toast in production to avoid spamming during development
  if (showToast && import.meta.env.PROD) {
    toast({
      title: `Error in ${context}`,
      description: err.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
  }

  return err.message || 'An unexpected error occurred';
};

/**
 * Test Supabase connection
 * @returns True if connection is successful, false otherwise
 */
export const testSupabaseConnection = async (): Promise<boolean> => {
  try {
    // Log the Supabase connection status
    console.log('Testing Supabase connection...');

    // Simple connection test - just try to get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.log('Session error (this is normal for unauthenticated users):', sessionError.message);
    }

    // Try a simple query to test database connection
    const { error: testError } = await supabase
      .from('categories')
      .select('id')
      .limit(1);

    if (testError) {
      console.error('Database connection test failed:', testError);
      return false;
    }

    console.log('Supabase connection test successful');
    return true;
  } catch (error) {
    console.error('Supabase connection test failed:', error);
    return false;
  }
};

/**
 * Generate a success toast notification
 * @param title Toast title
 * @param description Toast description
 */
export const showSuccessToast = (title: string, description: string): void => {
  toast({
    title,
    description,
  });
};

/**
 * Generate an error toast notification
 * @param title Toast title
 * @param description Toast description
 */
export const showErrorToast = (title: string, description: string): void => {
  toast({
    title,
    description,
    variant: 'destructive',
  });
};

/**
 * Extract file path from Supabase Storage URL
 * @param url The Supabase Storage URL
 * @returns Object containing bucket name and file path
 */
export const extractStoragePath = (url: string): { bucketName: string, filePath: string } | null => {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');

    // Assuming URL format is /storage/v1/object/public/bucket-name/file-path
    if (pathParts.length < 4) return null;

    const bucketName = pathParts[2];
    const filePath = pathParts.slice(3).join('/');

    return { bucketName, filePath };
  } catch (error) {
    console.error('Error extracting storage path:', error);
    return null;
  }
};

/**
 * Generate a unique filename for storage
 * @param file The file to upload
 * @param prefix Optional prefix for the filename (e.g., user ID or product ID)
 * @returns Unique filename
 */
export const generateUniqueFilename = (file: File, prefix?: string): string => {
  const fileExt = file.name.split('.').pop();
  const randomString = Math.random().toString(36).substring(2, 15);
  const timestamp = Date.now();

  return prefix
    ? `${prefix}/${timestamp}-${randomString}.${fileExt}`
    : `${timestamp}-${randomString}.${fileExt}`;
};

/**
 * Validate an image file
 * @param file The file to validate
 * @param maxSizeMB Maximum file size in MB (default: 5)
 * @returns Error message if validation fails, null if validation passes
 */
export const validateImageFile = (file: File, maxSizeMB: number = 5): string | null => {
  // Check file type
  const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!validTypes.includes(file.type)) {
    return 'File must be an image (JPEG, PNG, GIF, or WEBP)';
  }

  // Check file size
  const maxSize = maxSizeMB * 1024 * 1024;
  if (file.size > maxSize) {
    return `Image must be smaller than ${maxSizeMB}MB`;
  }

  return null;
};
