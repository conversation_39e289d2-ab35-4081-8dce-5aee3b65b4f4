-- Comprehensive fix for product reviews system
-- This script will fix all issues with product reviews, ratings, and user profiles

-- First, ensure the user_profiles table exists with correct structure
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'user_profiles'
  ) THEN
    CREATE TABLE user_profiles (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      display_name TEXT,
      first_name TEXT,
      last_name TEXT,
      email TEXT,
      role TEXT CHECK (role IN ('user', 'admin')) DEFAULT 'user',
      phone TEXT,
      dob DATE,
      street TEXT,
      city TEXT,
      state TEXT,
      postal_code TEXT,
      country TEXT,
      avatar_url TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Created user_profiles table';
  ELSE
    RAISE NOTICE 'user_profiles table already exists';
  END IF;
END $$;

-- Ensure the product_reviews table exists with correct structure
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'product_reviews'
  ) THEN
    CREATE TABLE product_reviews (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      product_id UUID REFERENCES products(id) ON DELETE CASCADE,
      user_id UUID REFERENCES auth.users(id),
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      comment TEXT,
      title TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Add indexes for better performance
    CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
    CREATE INDEX idx_product_reviews_user_id ON product_reviews(user_id);
    CREATE INDEX idx_product_reviews_created_at ON product_reviews(created_at);
    
    RAISE NOTICE 'Created product_reviews table';
  ELSE
    RAISE NOTICE 'product_reviews table already exists';
  END IF;
END $$;

-- Enable RLS on both tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Anyone can view product reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can insert their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can update their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can delete their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Admins can manage all reviews" ON product_reviews;

DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Anyone can view user profiles" ON user_profiles;

-- Create new, working policies for product_reviews
CREATE POLICY "Anyone can view product reviews"
  ON product_reviews FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can insert reviews"
  ON product_reviews FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews"
  ON product_reviews FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews"
  ON product_reviews FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for user_profiles
CREATE POLICY "Anyone can view user profiles"
  ON user_profiles FOR SELECT
  USING (true);

CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  USING (auth.uid() = id);

-- Create the has_user_purchased_product function
CREATE OR REPLACE FUNCTION has_user_purchased_product(
  input_user_id UUID,
  input_product_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = input_user_id
    AND oi.product_id = input_product_id
    AND o.status IN ('delivered', 'shipped')
  ) INTO has_purchased;
  
  RETURN COALESCE(has_purchased, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the product_ratings_summary view
DROP VIEW IF EXISTS product_ratings_summary CASCADE;
CREATE VIEW product_ratings_summary AS
SELECT 
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;

-- Create the user_purchasable_reviews view
DROP VIEW IF EXISTS user_purchasable_reviews CASCADE;
CREATE VIEW user_purchasable_reviews AS
SELECT DISTINCT
  o.user_id,
  oi.product_id,
  p.name as product_name,
  o.id as order_id,
  o.created_at as purchase_date,
  CASE WHEN pr.id IS NULL THEN false ELSE true END as has_reviewed
FROM
  orders o
  JOIN order_items oi ON o.id = oi.order_id
  JOIN products p ON oi.product_id = p.id
  LEFT JOIN product_reviews pr ON pr.product_id = oi.product_id AND pr.user_id = o.user_id
WHERE
  o.status IN ('delivered', 'shipped');

-- Create function to update product ratings
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
DECLARE
  avg_rating NUMERIC;
  review_count BIGINT;
BEGIN
  -- Get the average rating and review count
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
    COUNT(*)
  INTO
    avg_rating,
    review_count
  FROM product_reviews
  WHERE product_id = COALESCE(NEW.product_id, OLD.product_id);

  -- Update the products table with the new rating information
  BEGIN
    UPDATE products
    SET
      rating = avg_rating,
      review_count = review_count
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);
  EXCEPTION WHEN OTHERS THEN
    -- If the update fails, log the error but don't fail the transaction
    RAISE NOTICE 'Failed to update product rating: %', SQLERRM;
  END;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update product ratings when reviews change
DROP TRIGGER IF EXISTS update_product_rating_insert ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_update ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_delete ON product_reviews;

CREATE TRIGGER update_product_rating_insert
AFTER INSERT ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_update
AFTER UPDATE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_delete
AFTER DELETE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

-- Create profiles for existing users who don't have one
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1)),
  email,
  'user',
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE user_profiles.id = auth.users.id
)
ON CONFLICT (id) DO NOTHING;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON product_reviews TO authenticated;
GRANT UPDATE ON user_profiles TO authenticated;
GRANT EXECUTE ON FUNCTION has_user_purchased_product TO anon, authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION has_user_purchased_product IS 'Checks if a user has purchased a specific product';
COMMENT ON VIEW product_ratings_summary IS 'Provides a summary of ratings for all products';
COMMENT ON VIEW user_purchasable_reviews IS 'Shows products a user has purchased and can review';
COMMENT ON FUNCTION update_product_rating IS 'Updates product rating in the products table when reviews change';

-- Final verification
DO $$
BEGIN
  RAISE NOTICE 'Product reviews system setup completed successfully!';
  RAISE NOTICE 'Tables created: user_profiles, product_reviews';
  RAISE NOTICE 'Views created: product_ratings_summary, user_purchasable_reviews';
  RAISE NOTICE 'Functions created: has_user_purchased_product, update_product_rating';
  RAISE NOTICE 'RLS policies and triggers configured';
END $$;
