import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Add history API fallback for SPA routing
    historyApiFallback: true,
    proxy: {
      '/api/razorpay': {
        // Use Vercel dev server when available, fallback to Node.js server
        target: process.env.VERCEL_URL ? `http://localhost:3000` : `http://localhost:${process.env.VITE_SERVER_PORT || 3001}`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks - split by size and usage
          if (id.includes('node_modules')) {
            // Large chart libraries - separate chunk
            if (id.includes('recharts') || id.includes('d3-') || id.includes('victory-vendor')) {
              return 'chart-vendor';
            }
            // React ecosystem - core chunk
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react-vendor';
            }
            // UI libraries - separate chunk
            if (id.includes('@radix-ui') || id.includes('lucide-react')) {
              return 'ui-vendor';
            }
            // Query and state management
            if (id.includes('@tanstack/react-query') || id.includes('@tanstack/query-core')) {
              return 'query-vendor';
            }
            // Form libraries
            if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('zod')) {
              return 'form-vendor';
            }
            // Supabase
            if (id.includes('@supabase')) {
              return 'supabase-vendor';
            }
            // Date utilities
            if (id.includes('date-fns')) {
              return 'date-vendor';
            }
            // Other vendor libraries
            return 'vendor';
          }

          // App chunks - split by feature
          if (id.includes('/src/pages/Admin') || id.includes('/src/components/admin/')) {
            return 'admin-dashboard';
          }
          if (id.includes('/src/pages/Products') || id.includes('/src/components/products/')) {
            return 'products';
          }
          if (id.includes('/src/pages/Cart') || id.includes('/src/pages/Checkout') || id.includes('/src/pages/Orders')) {
            return 'commerce';
          }
        }
      }
    },
    chunkSizeWarningLimit: 1000, // Increase warning limit to 1MB - optimized for production
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // Remove specific console methods
        passes: 2 // Multiple passes for better compression
      },
      mangle: {
        safari10: true // Better Safari compatibility
      }
    },
    // Enable CSS code splitting
    cssCodeSplit: true,
    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query'
      ],
      exclude: [
        'recharts', // Lazy load charts
        'd3-scale',
        'd3-array'
      ]
    }
  },

}));
