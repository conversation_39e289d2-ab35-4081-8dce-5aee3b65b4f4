import React, { useState } from 'react';
import { X, MessageCircle } from 'lucide-react';
import { openWhatsApp, BUSINESS_WHATSAPP_NUMBER, WHATSAPP_MESSAGES } from '@/utils/whatsapp';

// WhatsApp Icon Component
const WhatsAppIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"
      fill="currentColor"
    />
  </svg>
);

interface FloatingWhatsAppProps {
  phoneNumber?: string;
  message?: string;
  position?: 'bottom-right' | 'bottom-left';
  showTooltip?: boolean;
}

const FloatingWhatsApp: React.FC<FloatingWhatsAppProps> = ({
  phoneNumber = BUSINESS_WHATSAPP_NUMBER,
  message = WHATSAPP_MESSAGES.general,
  position = 'bottom-right',
  showTooltip = true
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [showTooltipState, setShowTooltipState] = useState(showTooltip);

  const handleWhatsAppClick = () => {
    openWhatsApp(message, phoneNumber);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  const handleTooltipClose = () => {
    setShowTooltipState(false);
  };

  if (!isVisible) return null;

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6'
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50 flex flex-col items-end space-y-2`}>
      {/* Tooltip */}
      {showTooltipState && (
        <div className="relative bg-white rounded-lg shadow-lg border border-gray-200 p-3 max-w-xs animate-in slide-in-from-bottom-2 duration-300">
          <button
            type="button"
            onClick={handleTooltipClose}
            className="absolute -top-2 -right-2 bg-gray-100 hover:bg-gray-200 rounded-full p-1 transition-colors"
            aria-label="Close tooltip"
          >
            <X className="h-3 w-3 text-gray-600" />
          </button>
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-1">
              <WhatsAppIcon className="h-5 w-5 text-green-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 mb-1">
                Need help? Chat with us!
              </p>
              <p className="text-xs text-gray-600">
                Get instant support for your furniture needs
              </p>
            </div>
          </div>
          {/* Tooltip arrow */}
          <div className="absolute bottom-0 right-4 transform translate-y-full">
            <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-white"></div>
          </div>
        </div>
      )}

      {/* WhatsApp Button */}
      <div className="relative group">
        <button
          type="button"
          onClick={handleWhatsAppClick}
          className="bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-pulse"
          aria-label="Chat on WhatsApp"
        >
          <WhatsAppIcon className="h-6 w-6" />
        </button>

        {/* Ripple effect */}
        <div className="absolute inset-0 rounded-full bg-green-500 opacity-20 animate-ping"></div>

        {/* Hover tooltip */}
        <div className="absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
            Chat on WhatsApp
            <div className="absolute top-full right-2 transform -translate-x-1/2">
              <div className="w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-gray-900"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Close button for the entire widget */}
      <button
        type="button"
        onClick={handleClose}
        className="bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full p-2 shadow-md transition-colors duration-200 opacity-70 hover:opacity-100"
        aria-label="Close WhatsApp widget"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
};

export default FloatingWhatsApp;
