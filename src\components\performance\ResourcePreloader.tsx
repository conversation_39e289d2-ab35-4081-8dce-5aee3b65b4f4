import { useEffect } from 'react';

interface PreloadResource {
  href: string;
  as: 'image' | 'script' | 'style' | 'font';
  type?: string;
  crossorigin?: 'anonymous' | 'use-credentials';
  media?: string;
}

interface ResourcePreloaderProps {
  resources: PreloadResource[];
  priority?: boolean;
}

/**
 * Component to preload critical resources for better performance
 */
export const ResourcePreloader: React.FC<ResourcePreloaderProps> = ({
  resources,
  priority = false
}) => {
  useEffect(() => {
    // Prevent running on server side
    if (typeof window === 'undefined') return;

    const preloadedLinks: HTMLLinkElement[] = [];
    const processedUrls = new Set<string>();

    resources.forEach((resource) => {
      // Skip if already processed in this render
      if (processedUrls.has(resource.href)) return;
      processedUrls.add(resource.href);

      // Check if resource is already preloaded with same rel attribute
      const relType = priority ? 'preload' : 'prefetch';
      const existingLink = document.querySelector(
        `link[rel="${relType}"][href="${resource.href}"]`
      );

      if (!existingLink) {
        const link = document.createElement('link');
        link.rel = relType;
        link.href = resource.href;
        link.as = resource.as;

        if (resource.type) {
          link.type = resource.type;
        }

        if (resource.crossorigin) {
          link.crossOrigin = resource.crossorigin;
        }

        if (resource.media) {
          link.media = resource.media;
        }

        // Add to document head
        document.head.appendChild(link);
        preloadedLinks.push(link);
      }
    });

    // Cleanup function to remove preload links when component unmounts
    return () => {
      preloadedLinks.forEach((link) => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [resources, priority]);

  return null; // This component doesn't render anything
};

/**
 * Hook to preload critical images
 */
export const useImagePreloader = (images: string[], priority = false) => {
  useEffect(() => {
    if (!images.length) return;

    const preloadedImages: HTMLLinkElement[] = [];

    images.forEach((src) => {
      const link = document.createElement('link');
      link.rel = priority ? 'preload' : 'prefetch';
      link.href = src;
      link.as = 'image';

      document.head.appendChild(link);
      preloadedImages.push(link);
    });

    return () => {
      preloadedImages.forEach((link) => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [images, priority]);
};

/**
 * Hook to preload fonts
 */
export const useFontPreloader = (fonts: string[]) => {
  useEffect(() => {
    if (!fonts.length) return;

    const preloadedFonts: HTMLLinkElement[] = [];

    fonts.forEach((fontUrl) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = fontUrl;
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';

      document.head.appendChild(link);
      preloadedFonts.push(link);
    });

    return () => {
      preloadedFonts.forEach((link) => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [fonts]);
};

/**
 * Critical resource preloader for homepage
 */
export const HomepagePreloader = () => {
  const criticalResources: PreloadResource[] = [
    // Preload hero image (replace with actual hero image URL)
    {
      href: 'https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//logo.png',
      as: 'image'
    },
    // Preload critical CSS (if any external stylesheets)
    // Add more critical resources as needed
  ];

  return <ResourcePreloader resources={criticalResources} priority={true} />;
};

export default ResourcePreloader;
