import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { Product } from '@/types/product';
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';

export interface CartItem {
  product: Product;
  quantity: number;
}

interface CartContextType {
  cartItems: CartItem[];
  addToCart: (product: Product, quantity: number) => void;
  removeFromCart: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  itemCount: number;
  subtotal: number;
  isLoading: boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, isAuthenticated, isAdmin } = useAuth();
  const userId = user?.id || 'guest';

  // Load cart from Supabase or localStorage based on authentication state
  useEffect(() => {
    const loadCart = async () => {
      console.log('🔄 Loading cart...', { userId, isAuthenticated });
      setIsLoading(true);

      try {
        // Admin users should never have a cart
        if (isAdmin()) {
          console.log('👑 Admin user - no cart');
          setCartItems([]);
          return;
        }

        if (isAuthenticated && userId !== 'guest') {
          // For authenticated users, load cart from Supabase
          const { data, error } = await supabase
            .rpc('get_cart_items', { p_user_id: userId });

          if (error) {
            console.error('❌ Error fetching cart from Supabase:', error);
            console.log('🔄 Falling back to localStorage for authenticated user');
            // Fallback to localStorage if Supabase functions don't exist
            const cartKey = 'cart_guest';
            const storedCart = localStorage.getItem(cartKey);
            if (storedCart) {
              try {
                const parsedCart = JSON.parse(storedCart);
                if (Array.isArray(parsedCart)) {
                  setCartItems(parsedCart);
                  console.log('✅ Loaded cart from localStorage fallback:', parsedCart.length, 'items');
                }
              } catch (parseError) {
                console.error('Error parsing fallback cart:', parseError);
              }
            }
            return;
          }

          if (data && data.length > 0) {
            // Transform the data to match our CartItem structure
            const transformedItems = data.map(item => ({
              product: {
                id: item.product_id,
                name: item.product_name,
                price: item.product_price,
                salePrice: item.product_sale_price,
                image: item.product_image,
                // Add other product fields as needed
              } as Product,
              quantity: item.quantity
            }));

            setCartItems(transformedItems);
          } else {
            // Check if there's a localStorage cart to migrate
            migrateLocalStorageCart();
          }
        } else {
          // For guests, use localStorage
          const cartKey = 'cart_guest';
          const storedCart = localStorage.getItem(cartKey);

          if (storedCart) {
            try {
              setCartItems(JSON.parse(storedCart));
            } catch (error) {
              console.error(`Error parsing cart from localStorage:`, error);
              localStorage.removeItem(cartKey);
              setCartItems([]);
            }
          } else {
            setCartItems([]);
          }

          // Check for legacy cart format and migrate if needed
          const legacyCart = localStorage.getItem('cart');
          if (legacyCart) {
            try {
              const parsedLegacyCart = JSON.parse(legacyCart);
              if (parsedLegacyCart && parsedLegacyCart.length > 0) {
                localStorage.setItem(cartKey, legacyCart);
                setCartItems(parsedLegacyCart);
              }
              localStorage.removeItem('cart');
            } catch (error) {
              console.error('Error migrating legacy cart:', error);
              localStorage.removeItem('cart');
            }
          }
        }
      } catch (error) {
        console.error('Error loading cart:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCart();
  }, [userId, isAuthenticated, isAdmin]);

  // Migrate localStorage cart to Supabase when user logs in
  const migrateLocalStorageCart = useCallback(async () => {
    if (!isAuthenticated || userId === 'guest') return;

    // Check for user-specific cart in localStorage
    const cartKey = `cart_${userId}`;
    const storedCart = localStorage.getItem(cartKey) || localStorage.getItem('cart_guest');

    if (!storedCart) return;

    try {
      const parsedCart = JSON.parse(storedCart);
      if (!parsedCart || !parsedCart.length) return;

      // Add each item to Supabase cart
      for (const item of parsedCart) {
        await supabase.rpc('add_to_cart', {
          p_user_id: userId,
          p_product_id: item.product.id,
          p_quantity: item.quantity
        });
      }

      // Clear localStorage cart after migration
      localStorage.removeItem(cartKey);
      localStorage.removeItem('cart_guest');

      console.log('Migrated cart from localStorage to Supabase');

      // Reload cart from Supabase
      const { data, error } = await supabase
        .rpc('get_cart_items', { p_user_id: userId });

      if (error) throw error;

      if (data && data.length > 0) {
        const transformedItems = data.map(item => ({
          product: {
            id: item.product_id,
            name: item.product_name,
            price: item.product_price,
            salePrice: item.product_sale_price,
            image: item.product_image,
          } as Product,
          quantity: item.quantity
        }));

        setCartItems(transformedItems);
      }
    } catch (error) {
      console.error('Error migrating cart to Supabase:', error);
    }
  }, [userId, isAuthenticated]);

  // Save cart to appropriate storage whenever it changes
  useEffect(() => {
    const saveCart = async () => {
      // Don't save cart for admin users
      if (isAdmin()) return;

      if (isAuthenticated && userId !== 'guest') {
        // For authenticated users, cart is saved via the add/update/remove functions
        // No need to do anything here
      } else {
        // For guests, save to localStorage
        const cartKey = 'cart_guest';
        localStorage.setItem(cartKey, JSON.stringify(cartItems));
      }
    };

    saveCart();
  }, [cartItems, userId, isAuthenticated, isAdmin]);

  // Memoized cart calculations
  const itemCount = useMemo(() => {
    const count = cartItems.reduce((total, item) => total + item.quantity, 0);
    console.log('🔢 Cart item count:', count, 'from', cartItems.length, 'items');
    return count;
  }, [cartItems]);

  const subtotal = useMemo(() => {
    return cartItems.reduce((total, item) => {
      const price = item.product.salePrice || item.product.price;
      return total + price * item.quantity;
    }, 0);
  }, [cartItems]);

  // Add item to cart - memoized with useCallback
  const addToCart = useCallback(async (product: Product, quantity: number) => {
    console.log('🛒 Adding to cart:', { product: product.name, quantity, userId, isAuthenticated });
    setIsLoading(true);

    try {
      // Admin users should never have a cart
      if (isAdmin()) {
        console.log('❌ Admin user tried to add to cart');
        toast({
          title: "Admin Cart Disabled",
          description: "Admin users cannot add items to cart.",
          variant: "destructive"
        });
        return;
      }

      if (isAuthenticated && userId !== 'guest') {
        console.log('🔐 Authenticated user - adding to Supabase');
        // For authenticated users, add to Supabase
        const { error } = await supabase.rpc('add_to_cart', {
          p_user_id: userId,
          p_product_id: product.id,
          p_quantity: quantity
        });

        if (error) {
          console.error('❌ Supabase cart error:', error);
          console.log('🔄 Falling back to localStorage for authenticated user');
          // Fallback to localStorage if Supabase functions don't exist
          setCartItems(prevItems => {
            const existingItemIndex = prevItems.findIndex(item => item.product.id === product.id);

            if (existingItemIndex >= 0) {
              const updatedItems = [...prevItems];
              updatedItems[existingItemIndex] = {
                ...updatedItems[existingItemIndex],
                quantity: updatedItems[existingItemIndex].quantity + quantity
              };
              console.log('📝 Updated existing item in localStorage fallback:', updatedItems[existingItemIndex]);
              return updatedItems;
            } else {
              const newItems = [...prevItems, { product, quantity }];
              console.log('📝 Added new item to localStorage fallback:', { product: product.name, quantity });
              return newItems;
            }
          });

          toast({
            title: "Added to cart",
            description: `${quantity} × ${product.name} added to your cart.`,
          });
          return;
        }

        console.log('✅ Successfully added to Supabase cart');

        // Update local state
        setCartItems(prevItems => {
          const existingItemIndex = prevItems.findIndex(item => item.product.id === product.id);

          if (existingItemIndex >= 0) {
            const updatedItems = [...prevItems];
            updatedItems[existingItemIndex] = {
              ...updatedItems[existingItemIndex],
              quantity: updatedItems[existingItemIndex].quantity + quantity
            };
            console.log('📝 Updated existing item in cart:', updatedItems[existingItemIndex]);
            return updatedItems;
          } else {
            const newItems = [...prevItems, { product, quantity }];
            console.log('📝 Added new item to cart:', { product: product.name, quantity });
            return newItems;
          }
        });
      } else {
        console.log('👤 Guest user - adding to local storage');
        // For guests, update local state only
        setCartItems(prevItems => {
          const existingItemIndex = prevItems.findIndex(item => item.product.id === product.id);

          if (existingItemIndex >= 0) {
            const updatedItems = [...prevItems];
            updatedItems[existingItemIndex] = {
              ...updatedItems[existingItemIndex],
              quantity: updatedItems[existingItemIndex].quantity + quantity
            };
            console.log('📝 Updated existing item in guest cart:', updatedItems[existingItemIndex]);
            return updatedItems;
          } else {
            const newItems = [...prevItems, { product, quantity }];
            console.log('📝 Added new item to guest cart:', { product: product.name, quantity });
            return newItems;
          }
        });
      }

      toast({
        title: "Added to cart",
        description: `${quantity} × ${product.name} added to your cart.`,
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, userId, isAdmin, toast]);

  // Remove item from cart - memoized with useCallback
  const removeFromCart = useCallback(async (productId: string) => {
    setIsLoading(true);

    try {
      if (isAuthenticated && userId !== 'guest') {
        // For authenticated users, remove from Supabase
        const { error } = await supabase.rpc('update_cart_quantity', {
          p_user_id: userId,
          p_product_id: productId,
          p_quantity: 0 // Setting to 0 will remove the item
        });

        if (error) throw error;
      }

      // Update local state
      setCartItems(prevItems => prevItems.filter(item => item.product.id !== productId));

      toast({
        title: "Removed from cart",
        description: "Item removed from your cart.",
      });
    } catch (error) {
      console.error('Error removing from cart:', error);
      toast({
        title: "Error",
        description: "Failed to remove item from cart. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, userId, toast]);

  // Update item quantity - memoized with useCallback
  const updateQuantity = useCallback(async (productId: string, quantity: number) => {
    setIsLoading(true);

    try {
      if (quantity <= 0) {
        // If quantity is 0 or negative, remove the item
        await removeFromCart(productId);
        return;
      }

      if (isAuthenticated && userId !== 'guest') {
        // For authenticated users, update in Supabase
        const { error } = await supabase.rpc('update_cart_quantity', {
          p_user_id: userId,
          p_product_id: productId,
          p_quantity: quantity
        });

        if (error) throw error;
      }

      // Update local state
      setCartItems(prevItems =>
        prevItems.map(item =>
          item.product.id === productId
            ? { ...item, quantity }
            : item
        )
      );
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      toast({
        title: "Error",
        description: "Failed to update item quantity. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, userId, removeFromCart]);

  // Clear cart - memoized with useCallback
  const clearCart = useCallback(async () => {
    setIsLoading(true);

    try {
      if (isAuthenticated && userId !== 'guest') {
        // For authenticated users, clear cart in Supabase
        const { error } = await supabase.rpc('clear_cart', {
          p_user_id: userId
        });

        if (error) throw error;
      } else {
        // For guests, clear localStorage
        localStorage.removeItem('cart_guest');
      }

      // Also remove legacy cart if it exists
      localStorage.removeItem('cart');

      // Update local state
      setCartItems([]);

      toast({
        title: "Cart cleared",
        description: "All items have been removed from your cart.",
      });
    } catch (error) {
      console.error('Error clearing cart:', error);
      toast({
        title: "Error",
        description: "Failed to clear cart. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, userId, toast]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    itemCount,
    subtotal,
    isLoading
  }), [
    cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    itemCount,
    subtotal,
    isLoading
  ]);

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
