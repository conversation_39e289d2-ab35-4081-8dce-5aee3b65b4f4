import { createRoot } from 'react-dom/client'
import { StrictMode } from 'react'
import App from './App.tsx'
import './index.css'
import { registerSW } from './utils/serviceWorker'

// Register service worker for caching and performance
if (import.meta.env.PROD) {
  registerSW();
}

// Wrap the app in StrictMode for better development experience
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
