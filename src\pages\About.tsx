
import React, { useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Link } from 'react-router-dom';
import { ArrowRight, Check } from 'lucide-react';

const About = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen">
      <Navbar />

      <div className="pt-28 pb-16">
        {/* Our Story */}
        <div className="py-16 md:py-24">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <img
                  src="https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//factory.jpg"
                  alt="Craftsman working on furniture"
                  className="rounded-xl shadow-xl w-full h-auto object-cover"
                />
              </div>
              <div>
                <h2 className="heading-2 text-badhees-800 mb-6">Our Story</h2>
                <p className="text-badhees-600 mb-6">
                  Founded on January 26th, 2023, The Badhees is a design-driven interior-based factory committed to creating thoughtful, high-quality spaces. With a focus on craftsmanship, innovation, and aesthetic harmony.
                </p>
                <p className="text-badhees-600 mb-6">
                  We specialize in custom interiors that blend functionality with timeless style. At The Badhees, we believe that every space has a story and we are here to help you tell yours through design that lasts.
                </p>
                <p className="text-badhees-600">
                  Every piece that leaves our workshop is created with intention, designed to become a cherished part of your home for generations to come.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Our Values */}
        <div className="py-16 md:py-24 bg-badhees-50">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="heading-2 text-badhees-800 mb-6">Our Values</h2>
              <p className="text-badhees-600">
                At the heart of everything we do are the values that guide our work and define our approach to furniture making.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  title: "Craftsmanship",
                  description: "We believe in the value of skilled hands and meticulous attention to detail. Each piece is crafted with precision and care."
                },
                {
                  title: "Sustainability",
                  description: "We're committed to responsible sourcing, using FSC-certified woods and eco-friendly materials to minimize our environmental impact."
                },
                {
                  title: "Timeless Design",
                  description: "We create furniture that transcends trends, combining classic principles with contemporary sensibilities for enduring appeal."
                }
              ].map((value, index) => (
                <div key={index} className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                  <div className="w-12 h-12 bg-badhees-100 rounded-full flex items-center justify-center mb-6">
                    <Check className="h-6 w-6 text-badhees-accent" />
                  </div>
                  <h3 className="text-xl font-bold text-badhees-800 mb-4">{value.title}</h3>
                  <p className="text-badhees-600">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Our Team */}
        <div className="py-16 md:py-24">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="heading-2 text-badhees-800 mb-6">Meet Our Team</h2>
              <p className="text-badhees-600">
                Behind every beautiful piece of furniture is a team of passionate craftspeople and designers dedicated to excellence.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  name: "Vinay Sharma",
                  role: "Founder & Master Craftsman",
                  image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//vs.jpg"
                },
                {
                  name: "Vikas Kumar Sharma",
                  role: "CEO & MD ",
                  image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//vks.jpg"
                },
                {
                  name: "Manish Kumar Sharma",
                  role: "Head of Design & Creative director",
                  image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//man.jpg"
                },
                {
                  name: "Mithun Kumar Sharma",
                  role: "Associate Consultant",
                  image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//mks.jpg"
                }
              ].map((member, index) => (
                <div key={index} className="text-center">
                  <div className="w-40 h-40 rounded-full mx-auto overflow-hidden mb-6">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-lg font-bold text-badhees-800 mb-1">{member.name}</h3>
                  <p className="text-sm text-badhees-500">{member.role}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="py-16 md:py-24 bg-badhees-800 text-white">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Experience The Badhees Difference</h2>
            <p className="text-badhees-100 max-w-2xl mx-auto mb-8">
              Join us in our mission to create homes filled with beauty, comfort, and meaning through thoughtfully crafted furniture.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link to="/products" className="bg-white text-badhees-800 px-6 py-3 rounded-md font-medium hover:bg-badhees-50 transition-colors">
                Shop Products
              </Link>
              <Link to="/contact" className="bg-transparent border border-white text-white px-6 py-3 rounded-md font-medium hover:bg-white/10 transition-colors">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default About;
