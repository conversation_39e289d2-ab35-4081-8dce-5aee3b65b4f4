
import React, { useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import ResponsiveLayout from "@/components/layout/ResponsiveLayout";
import PageContainer from "@/components/layout/PageContainer";
import HeroBanner from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import CategoryShowcase from "@/components/home/<USER>";
import { Button } from "@/components/ui/button";
import { LayoutDashboard, Settings } from "lucide-react";
import { useAuth } from "@/context/SupabaseAuthContext";
import { useIsMobile } from "@/hooks/use-responsive";
import { usePerformanceMonitor } from "@/hooks/use-performance-monitor";
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { useQueryClient } from "@tanstack/react-query";
import { useTabVisibility } from "@/hooks/use-optimized-render";
import { HomepagePreloader } from "@/components/performance/ResourcePreloader";

const Index = () => {
  const { isAuthenticated, isAdmin } = useAuth();
  const isMobile = useIsMobile();
  const { isVisible } = usePerformanceMonitor('HomePage');
  const queryClient = useQueryClient();

  // Handle tab visibility changes - refetch data when tab becomes visible
  const handleTabVisible = useCallback(() => {
    console.log('Tab became visible - refreshing home page data');
    queryClient.invalidateQueries({ queryKey: ['products'] });
    window.dispatchEvent(new CustomEvent('product-data-updated'));
  }, [queryClient]);

  useTabVisibility(handleTabVisible);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    try {
      // Invalidate all product-related queries
      await queryClient.invalidateQueries({ queryKey: ['products'] });

      // Dispatch custom event for components to refresh
      window.dispatchEvent(new CustomEvent('product-data-updated'));
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  }, [queryClient]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Force a refresh of the component when coming back to the home page
    // This ensures newly added products are displayed
    const cleanupListener = () => {
      // Add a small timeout to ensure components have time to fetch fresh data
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('product-data-updated'));
      }, 100);
    };

    // Listen for navigation events that might have updated products
    window.addEventListener('popstate', cleanupListener);

    return () => {
      window.removeEventListener('popstate', cleanupListener);
    };
  }, []);

  return (
    <ResponsiveLayout hideFooter={false}>
      <HomepagePreloader />
      <PullToRefresh onRefresh={handleRefresh}>
        {/* Admin Panel Button(s) - Only visible for admin users */}
        {isAuthenticated && isAdmin() && (
          <div className="fixed bottom-4 md:bottom-8 right-4 md:right-8 z-40 flex gap-2">
            <Link to="/admin">
              <Button size={isMobile ? "sm" : "default"} className="shadow-lg flex items-center gap-2 bg-badhees-800 hover:bg-badhees-700">
                <LayoutDashboard className="h-4 w-4" />
                <span className={isMobile ? "sr-only" : ""}>Dashboard</span>
              </Button>
            </Link>
            <Link to="/admin/products">
              <Button size={isMobile ? "sm" : "default"} className="shadow-lg flex items-center gap-2 bg-gray-700 hover:bg-gray-600">
                <Settings className="h-4 w-4" />
                <span className={isMobile ? "sr-only" : ""}>Admin Panel</span>
              </Button>
            </Link>
          </div>
        )}

        {/* Main Content */}
        <HeroBanner />
        <FeaturedProducts />
        <CategoryShowcase />

        {/* Enhanced navigation to Custom Projects page */}
        <div className="bg-badhees-50 py-6 sm:py-8 md:py-10">
          <PageContainer>
            <div className="text-center">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-badhees-800 mb-3 sm:mb-4 md:mb-6">
                Explore Our Custom Projects
              </h2>
              <p className="text-base sm:text-lg text-badhees-600 max-w-2xl mx-auto mb-6 md:mb-8 px-4 sm:px-0">
                Discover our curated custom projects and premium interior design services.
              </p>
              <Link to="/custom-interiors">
                <Button size={isMobile ? "default" : "lg"} className="bg-badhees-accent hover:bg-badhees-accent/90">
                  View Custom Projects
                </Button>
              </Link>
            </div>
          </PageContainer>
        </div>
      </PullToRefresh>
    </ResponsiveLayout>
  );
};

export default Index;
